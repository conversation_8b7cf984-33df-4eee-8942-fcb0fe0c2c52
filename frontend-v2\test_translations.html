<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Test</title>
</head>
<body>
    <h1>Translation Test</h1>
    
    <div>
        <h2>Certifications Test</h2>
        <p data-i18n="certifications.items0">Loading...</p>
        <p data-i18n="certifications.items1">Loading...</p>
        <p data-i18n="certifications.items2">Loading...</p>
        <p data-i18n="certifications.items3">Loading...</p>
        <p data-i18n="certifications.items4">Loading...</p>
    </div>
    
    <div>
        <h2>Portfolio Test</h2>
        <p data-i18n="portfolio.portfolioProject0title">Loading...</p>
        <p data-i18n="portfolio.portfolioProject0description">Loading...</p>
        <p data-i18n="portfolio.portfolioProject1title">Loading...</p>
        <p data-i18n="portfolio.portfolioProject1description">Loading...</p>
    </div>
    
    <div>
        <h2>Experience Test</h2>
        <p data-i18n="experience.jobs0Company">Loading...</p>
        <p data-i18n="experience.jobs0position">Loading...</p>
        <p data-i18n="experience.jobs0description">Loading...</p>
        <p data-i18n="experience.jobs1company">Loading...</p>
        <p data-i18n="experience.jobs1position">Loading...</p>
        <p data-i18n="experience.jobs1description">Loading...</p>
    </div>
    
    <div>
        <h2>Blog Test</h2>
        <p data-i18n="blog.posts0title">Loading...</p>
        <p data-i18n="blog.posts0excerpt">Loading...</p>
    </div>
    
    <button onclick="switchLanguage('en')">English</button>
    <button onclick="switchLanguage('de')">German</button>
    
    <script src="./lang.js"></script>
    <script>
        function switchLanguage(lang) {
            window.i18n.switchLanguage(lang);
        }
    </script>
</body>
</html>
